import { CrudService, ICrudOption } from '../crudService.pg';
import { CarOption, CarOptionGroup } from '@/models';
import { config } from '@/config';
import { errorService } from '@/services';

export class CarOptionService extends CrudService<typeof CarOption> {
  constructor() {
    super(CarOption);
  }

  async getList(
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ['defaultScope'],
    }
  ) {
    return await this.exec(
      this.modelWithScope(option.scope).findAll({
        include: [
          {
            association: 'option_group',
            attributes: ['id', 'name', 'description']
          }
        ],
        limit: option.limit,
        offset: option.offset,
        order: [['index', 'ASC'], ['created_at', 'DESC']],
        where: option.filter || {}
      })
    );
  }

  async getItem(option: ICrudOption) {
    return await this.exec(
      this.modelWithScope(option.scope).findOne({
        include: [
          {
            association: 'option_group',
            attributes: ['id', 'name', 'description']
          }
        ],
        where: option.filter
      })
    );
  }

  /**
   * Enhanced create method with relationship validation
   */
  async create(params: any, option?: ICrudOption) {
    // Validate required fields
    if (!params.name || !params.name.trim()) {
      throw errorService.database.queryFail('Car option name is required', 400);
    }

    if (!params.option_group_id) {
      throw errorService.database.queryFail('Option group ID is required', 400);
    }

    // Validate option group exists
    const optionGroup = await this.exec(
      CarOptionGroup.findByPk(params.option_group_id),
      { allowNull: true }
    );

    if (!optionGroup) {
      throw errorService.database.queryFail('Invalid option group ID: option group does not exist', 400);
    }

    // Check for duplicate option name within the same option group
    const existingOption = await this.exec(
      CarOption.findOne({
        where: {
          option_group_id: params.option_group_id,
          name: {
            [require('sequelize').Op.iLike]: params.name.trim()
          }
        }
      }),
      { allowNull: true }
    );

    if (existingOption) {
      throw errorService.database.queryFail(
        'An option with this name already exists in this option group', 
        409
      );
    }

    return await super.create(params, option);
  }

  /**
   * Enhanced update method with relationship validation
   */
  async update(params: any, option?: ICrudOption) {
    // Validate name if provided
    if (params.name && !params.name.trim()) {
      throw errorService.database.queryFail('Car option name cannot be empty', 400);
    }

    // Validate option group if being updated
    if (params.option_group_id) {
      const optionGroup = await this.exec(
        CarOptionGroup.findByPk(params.option_group_id),
        { allowNull: true }
      );

      if (!optionGroup) {
        throw errorService.database.queryFail('Invalid option group ID: option group does not exist', 400);
      }
    }

    // Check for duplicate option name if name or option group is being updated
    if (params.name || params.option_group_id) {
      const currentOption = await this.getItem(option);
      const optionGroupId = params.option_group_id || currentOption.option_group_id;
      const optionName = params.name || currentOption.name;

      const existingOption = await this.exec(
        CarOption.findOne({
          where: {
            option_group_id: optionGroupId,
            name: {
              [require('sequelize').Op.iLike]: optionName.trim()
            },
            id: {
              [require('sequelize').Op.ne]: option.filter.id
            }
          }
        }),
        { allowNull: true }
      );

      if (existingOption) {
        throw errorService.database.queryFail(
          'An option with this name already exists in this option group', 
          409
        );
      }
    }

    return await super.update(params, option);
  }

  /**
   * Get options with option group information
   */
  async getOptionsWithGroup(optionGroupId?: string) {
    const whereClause = optionGroupId ? { option_group_id: optionGroupId } : {};

    return await this.exec(
      CarOption.findAll({
        where: whereClause,
        include: [{
          model: CarOptionGroup,
          as: 'option_group',
          attributes: ['id', 'name', 'description']
        }],
        order: [['index', 'ASC'], ['name', 'ASC']]
      })
    );
  }

  /**
   * Get premium options
   */
  async getPremiumOptions() {
    return await this.exec(
      CarOption.findAll({
        where: { is_premium: true },
        include: [{
          model: CarOptionGroup,
          as: 'option_group',
          attributes: ['id', 'name']
        }],
        order: [['name', 'ASC']]
      })
    );
  }
}
