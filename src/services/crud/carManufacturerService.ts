import {CrudService, ICrudOption} from '../crudService.pg';
import {CarManufacturer, CarModel} from '@/models';
import { errorService } from '@/services';

export class CarManufacturerService extends CrudService<typeof CarManufacturer> {
    constructor() {
        super(CarManufacturer);
    }

    /**
     * Override delete method to check for relationship constraints
     * Prevents deletion of manufacturer if it has associated car models
     */
    async delete(option?: ICrudOption) {
        // First check if the manufacturer exists
        const manufacturer = await this.getItem(option);
        
        // Check for associated car models
        const associatedModels = await this.exec(
            CarModel.count({
                where: { 
                    manufacturer_id: manufacturer.id,
                    deleted_at: null  // Only count non-deleted models
                }
            })
        );

        if (associatedModels > 0) {
            throw errorService.database.queryFail(
                `Cannot delete manufacturer with ${associatedModels} associated car model(s). Please delete or reassign the models first.`,
                409
            );
        }

        // Proceed with deletion if no constraints violated
        return await super.delete(option);
    }

    /**
     * Enhanced create method with validation
     */
    async create(params: any, option?: ICrudOption) {
        // Validate required fields
        if (!params.name || !params.name.trim()) {
            throw errorService.database.queryFail('Manufacturer name is required', 400);
        }

        // Check for duplicate name (case-insensitive)
        const existingManufacturer = await this.exec(
            CarManufacturer.findOne({
                where: {
                    name: {
                        [require('sequelize').Op.iLike]: params.name.trim()
                    }
                }
            }),
            { allowNull: true }
        );

        if (existingManufacturer) {
            throw errorService.database.queryFail('Manufacturer with this name already exists', 409);
        }

        return await super.create(params, option);
    }

    /**
     * Enhanced update method with validation
     */
    async update(params: any, option?: ICrudOption) {
        // Validate name if provided
        if (params.name && !params.name.trim()) {
            throw errorService.database.queryFail('Manufacturer name cannot be empty', 400);
        }

        // Check for duplicate name if name is being updated
        if (params.name) {
            const existingManufacturer = await this.exec(
                CarManufacturer.findOne({
                    where: {
                        name: {
                            [require('sequelize').Op.iLike]: params.name.trim()
                        },
                        id: {
                            [require('sequelize').Op.ne]: option.filter.id
                        }
                    }
                }),
                { allowNull: true }
            );

            if (existingManufacturer) {
                throw errorService.database.queryFail('Manufacturer with this name already exists', 409);
            }
        }

        return await super.update(params, option);
    }

    /**
     * Get manufacturer with associated models count
     */
    async getManufacturerWithModelCount(manufacturerId: string) {
        const manufacturer = await this.exec(
            CarManufacturer.findByPk(manufacturerId, {
                include: [{
                    model: CarModel,
                    as: 'models',
                    attributes: ['id'],
                    where: { deleted_at: null },
                    required: false
                }]
            }),
            { allowNull: false }
        );

        return {
            ...manufacturer.toJSON(),
            models_count: manufacturer.models ? manufacturer.models.length : 0
        };
    }
} 