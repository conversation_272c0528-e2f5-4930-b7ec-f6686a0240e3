import { CrudService } from '../crudService';
import { ICrudOption } from '../';
import { UsedCarSavedSearch } from '@/models';

export class UsedCarSavedSearchService extends CrudService<typeof UsedCarSavedSearch> {
  constructor() {
    super(UsedCarSavedSearch);
  }

  /**
   * Get all saved searches for a user
   */
  async getUserSavedSearches(userId: string, option?: ICrudOption) {
    const searches = await this.exec(
      this.model.findAll({
        where: {
          user_id: userId,
          status: true,
        },
        order: [['created_at', 'DESC']],
        ...option?.queryInfo,
      })
    );

    return {
      rows: searches,
      count: searches.length,
    };
  }

  /**
   * Create a new saved search for a user
   */
  async createSavedSearch(params: any, option?: ICrudOption) {
    // Ensure user_id is set
    if (!params.user_id) {
      throw new Error('User ID is required');
    }

    const savedSearch = await this.exec(
      this.model.create(params)
    );

    return savedSearch;
  }

  /**
   * Delete a saved search (soft delete)
   */
  async deleteSavedSearch(searchId: string, userId: string, option?: ICrudOption) {
    const savedSearch = await this.exec(
      this.model.findOne({
        where: {
          id: searchId,
          user_id: userId,
          status: true,
        },
      }),
      {
        allowNull: false,
      }
    );

    await this.exec(
      this.model.destroy({
        where: {
          id: searchId,
          user_id: userId,
        },
      })
    );

    return { success: true, message: 'Saved search deleted successfully' };
  }

  /**
   * Update a saved search
   */
  async updateSavedSearch(searchId: string, userId: string, params: any, option?: ICrudOption) {
    const savedSearch = await this.exec(
      this.model.findOne({
        where: {
          id: searchId,
          user_id: userId,
          status: true,
        },
      }),
      {
        allowNull: false,
      }
    );

    const updatedSearch = await this.exec(
      savedSearch.update(params)
    );

    return updatedSearch;
  }
}

export const usedCarSavedSearchService = new UsedCarSavedSearchService(); 