import {Crud<PERSON>ontroller} from './crudController'
import {Auth<PERSON>ontroller} from './authController'
// import { KeywordController } from './crud/keywordController';

import {EmployeeController} from './crud/employeeController'
import {PushNotificationController} from './crud/pushNotificationController'
import {UserController} from './crud/userController'
// import { EventController } from './crud/eventController';
import {CityController} from './crud/cityController'
import {DistrictController} from './crud/districtController'
import {SettingController} from './crud/settingController'
import {CategoryController} from './crud/categoryController'
import {TagController} from './crud/tagController'
import {ThemaController} from './crud/themaController'
import {PostController} from './crud/postController'
import {CommentController} from './crud/commentController'
import {ShopController} from './crud/shopController'
import {EventController} from './crud/eventController'
import {ShopTagController} from './crud/shopTagController'
import {FavouriteController} from './crud/favouriteController'
import {ReviewController} from './crud/reviewController'
import {WardController} from './crud/wardController'
import {FavouritePostController} from './crud/favouritePostController'
import {LinkController} from './crud/linkController'
import {LinkCategoryController} from './crud/linkCategoryController'
import {DislikePostController} from './crud/dislikePostController'
import {SettingUserPermissionController} from './crud/settingUserPermissionController'
import {BannerController} from './crud/bannerController'
import {NotificationController} from './crud/notificationController'
import {RecentReadingController} from './crud/recentReadingController'
import {ContactController} from './crud/contactController'
import {ContentController} from './crud/contentController'
import {StatisticController} from './crud/statisticController'
import {RecordController} from './crud/recordController'
import {SeoController} from './crud/seoController'
import {MetaController} from './crud/metaController'
import {RecruitController} from './crud/recruitController'
import {KLocationController} from './crud/locationController'
import {HistoryController} from './crud/historyController'
import {ReportController} from './crud/reportController'
import {FaqController} from './crud/faqController'
import {FaqCategoryController} from './crud/faqCategoryController'
import {CourseController} from './crud/courseController'
import {PriceController} from './crud/priceController'
import {ReservationController} from './crud/reservationController'
import {ReservationItemController} from './crud/reservationItemController'
import {MentorController} from './crud/mentorController'
import {FavouriteMentorController} from './crud/favouriteMentorController'
import {LoyaltyController} from './crud/loyaltyController'
import {AdminSettingController} from './crud/adminSettingController'
import {BlockController} from './crud/blockController'
import {ShortVideoController} from './crud/shortVideoController'
import {FavouriteShortVideoController} from './crud/favouriteShortVideoController'
import {QuestionController} from './crud/questionController'
import {AnswerQuestionController} from './crud/answerQuestionController'
import {UserPaymentHistoryController} from './crud/userPaymentHistoryController'
import {TicketUsedController} from './crud/ticketUsedController'
import {AppVersionController} from './crud/appVersionController'
import {ConversationController} from './crud/conversationController'
import {MessageController} from './crud/messageController'
import {PointProductController} from './crud/pointProductController'
import {PointController} from './crud/pointController'
import {PointProductHistoryController} from './crud/pointProductHistoryController'
import {FeedbackController} from './crud/feedbackController'
import {FeedbackItemController} from './crud/feedbackItemController'
import {SeoSsrController} from './crud/seoSsrController'
import {BlogController} from './crud/blogController'
import {GroupController} from "@/controllers/crud/groupController";
import {NavIconController} from "@/controllers/crud/navIconController";
import {NavbarController} from "@/controllers/crud/navbarController";
import {SettingProvinceController} from "@/controllers/crud/settingProvinceController";
import {SettingDistrictController} from "@/controllers/crud/settingDistrictController";
import {SettingStationController} from "@/controllers/crud/settingStationController";
import {SettingStationLineController} from "@/controllers/crud/settingStationLineController";
import {SettingStationSubwayController} from "@/controllers/crud/settingStationSubwayController";
import {SiteController} from "@/controllers/crud/siteController";
import {SiteCategoryController} from "@/controllers/crud/siteCategoryController";
import {RealEstateController} from "@/controllers/crud/realEstateController";
import {RealEstateTagController} from "@/controllers/crud/realEstateTagController";
import {SecondHandMarketController} from "@/controllers/crud/secondHandMarketController";
import {PopupController} from "@/controllers/crud/popupController";
import {KeywordCategoryController} from "@/controllers/crud/keywordCategoryController";
import {KeywordController} from "@/controllers/crud/keywordController";
import { KeywordTypeController } from "@/controllers/crud/keywordTypeController";
import { SiteBannerController } from "@/controllers/crud/siteBannerController";
import { JobRequestController } from "@/controllers/crud/jobRequestController"; 
import { QuoteController } from "@/controllers/crud/quoteController";
import { FavoriteJobController } from '@/controllers/crud/favoriteJob'
import { ExpertInfoController } from '@/controllers//crud/expertInfoController'
import { AdvertisingImageController } from '@/controllers/crud/advertisingImageController'
import { ActionLogController } from './crud/actionLogController'
import { BoardPermissionController } from './crud/boardPermissionController'
import { BoardController } from './crud/boardController'
import { ExpRuleController } from './crud/expRuleController'
import { LevelController } from './crud/levelController'
import { RankingController } from './crud/rankingController'
import { PopupWindowController } from './crud/popupWindowController'
import { UsedCarPostController } from './crud/usedCarPostController'
import { UsedCarSavedSearchController } from './crud/usedCarSavedSearchController'
import { UsedCarInquiryController } from './crud/usedCarInquiryController'
import { FavouriteUsedCarPostController } from './crud/favouriteUsedCarPostController'
import { CarManufacturerController } from './crud/carManufacturerController'
import { CarModelController } from './crud/carModelController'
import { CarOptionGroupController } from './crud/carOptionGroupController'
import { CarOptionController } from './crud/carOptionController'

const authController = new AuthController()

// Crud
const employeeController = new EmployeeController()
const pushNotificationController = new PushNotificationController()
const userController = new UserController()
// const eventController = new EventController();
const cityController = new CityController()
const districtController = new DistrictController()
const settingController = new SettingController()
const categoryController = new CategoryController()
const tagController = new TagController()
const themaController = new ThemaController()
const postController = new PostController()
const commentController = new CommentController()
const shopController = new ShopController()
const eventController = new EventController()
const shopTagController = new ShopTagController()
const reviewController = new ReviewController()
const favouriteController = new FavouriteController()
const wardController = new WardController()
const favouritePostController = new FavouritePostController()
const linkController = new LinkController()
const linkCategoryController = new LinkCategoryController()
const dislikePostController = new DislikePostController()
const settingUserPermissionController = new SettingUserPermissionController()
const bannerController = new BannerController()
const notificationController = new NotificationController()
const recentReadingController = new RecentReadingController()
const contactController = new ContactController()
const contentController = new ContentController()
const statisticController = new StatisticController()
const recordController = new RecordController()
const seoController = new SeoController()
const metaController = new MetaController()
const recruitController = new RecruitController()
const kLocationController = new KLocationController()
const historyController = new HistoryController()
const reportController = new ReportController()
const faqController = new FaqController()
const faqCategoryController = new FaqCategoryController()
const courseController = new CourseController()
const priceController = new PriceController()
const reservationController = new ReservationController()
const reservationItemController = new ReservationItemController()
const mentorController = new MentorController()
const favouriteMentorController = new FavouriteMentorController()
const loyaltyController = new LoyaltyController()
const adminSettingController = new AdminSettingController()
const blockController = new BlockController()
const shortVideoController = new ShortVideoController()
const favouriteShortVideoController = new FavouriteShortVideoController()
const questionController = new QuestionController()
const answerQuestionController = new AnswerQuestionController()
const userPaymentHistoryController = new UserPaymentHistoryController()
const ticketUsedController = new TicketUsedController()
const appVersionController = new AppVersionController()
const conversationController = new ConversationController()
const messageController = new MessageController()
const pointProductController = new PointProductController()
const pointController = new PointController()
const pointProductHistoryController = new PointProductHistoryController()
const feedbackController = new FeedbackController()
const feedbackItemController = new FeedbackItemController()
const seoSsrController = new SeoSsrController()
const blogController = new BlogController()
const groupController = new GroupController()
const navIconController = new NavIconController()
const navbarController = new NavbarController()
const settingProvinceController = new SettingProvinceController()
const settingDistrictController = new SettingDistrictController()
const settingStationController = new SettingStationController()
const settingStationLineController = new SettingStationLineController()
const settingStationSubwayController = new SettingStationSubwayController()

const siteController = new SiteController()
const siteCategoryController = new SiteCategoryController()

const realEstateController = new RealEstateController()

const realEstateTagController = new RealEstateTagController()

const secondHandMarketController = new SecondHandMarketController()
const popupController = new PopupController()

const keywordCategoryController = new KeywordCategoryController()

const keywordController = new KeywordController()

const keywordTypeController = new KeywordTypeController()

const siteBannerController = new SiteBannerController()

const jobRequestController = new JobRequestController()

const quoteController = new QuoteController()

const favoriteJobController = new FavoriteJobController()

const expertInfoController = new ExpertInfoController()

const advertisingImageController = new AdvertisingImageController()

const actionLogController = new ActionLogController()

const boardPermissionController = new BoardPermissionController()

const boardController = new BoardController()

const expRuleController = new ExpRuleController()

const levelController = new LevelController()

const rankingController = new RankingController()

const popupWindowController = new PopupWindowController()

const usedCarPostController = new UsedCarPostController()
const usedCarSavedSearchController = new UsedCarSavedSearchController()
const usedCarInquiryController = new UsedCarInquiryController()
const favouriteUsedCarPostController = new FavouriteUsedCarPostController()
const carManufacturerController = new CarManufacturerController()
const carModelController = new CarModelController()
const carOptionGroupController = new CarOptionGroupController()
const carOptionController = new CarOptionController()

export {
  // keywordController,
  employeeController,
  userController,
  districtController,
  cityController,
  postController,
  commentController,
  shopController,
  eventController,
  shopTagController,
  reviewController,
  favouriteController,
  settingUserPermissionController,
  bannerController,
  notificationController,
  recentReadingController,
  contactController,
  statisticController,
  recordController,
  metaController,
  recruitController,
  reportController,
  //   eventController,
  settingController,
  categoryController,
  tagController,
  themaController,
  pushNotificationController,
  authController,
  wardController,
  favouritePostController,
  linkController,
  linkCategoryController,
  dislikePostController,
  contentController,
  seoController,
  kLocationController,
  historyController,
  faqController,
  faqCategoryController,
  courseController,
  priceController,
  mentorController,
  loyaltyController,
  adminSettingController,
  reservationController,
  reservationItemController,
  favouriteMentorController,
  blockController,
  shortVideoController,
  favouriteShortVideoController,
  questionController,
  answerQuestionController,
  userPaymentHistoryController,
  ticketUsedController,
  appVersionController,
  conversationController,
  messageController,
  pointProductController,
  pointController,
  pointProductHistoryController,
  feedbackController,
  feedbackItemController,
  seoSsrController,
  blogController,
  groupController,
  CrudController,
  navIconController,
  navbarController,
  settingProvinceController,
  settingDistrictController,
  settingStationController,
  settingStationLineController,
  settingStationSubwayController,
  siteController,
  siteCategoryController,
  realEstateController,
  realEstateTagController,
  secondHandMarketController,
  popupController,
  keywordCategoryController,
  keywordController,
  keywordTypeController,
  siteBannerController,
  jobRequestController,
  quoteController,
  favoriteJobController,
  expertInfoController,
  advertisingImageController,
  actionLogController,
  boardPermissionController,
  boardController,
  expRuleController,
  levelController,
  rankingController,
  popupWindowController,
  usedCarPostController,
  usedCarSavedSearchController,
  usedCarInquiryController,
  favouriteUsedCarPostController,
  carManufacturerController,
  carModelController,
  carOptionGroupController,
  carOptionController
};
