import { CrudService } from '../crudService';
import { ICrudOption } from '../';
import { UsedCarInquiry, UsedCarPost, User } from '@/models';
import { Op } from 'sequelize';

export class UsedCarInquiryService extends CrudService<typeof UsedCarInquiry> {
  constructor() {
    super(UsedCarInquiry);
  }

  /**
   * Get all inquiries sent by a user (buyer)
   */
  async getUserInquiries(userId: string, option?: ICrudOption) {
    const inquiries = await this.exec(
      this.model.findAll({
        where: {
          buyer_id: userId,
          status: true,
        },
        include: [
          {
            model: UsedCarPost,
            as: 'UsedCarPost',
            where: { status: true },
            required: true,
            attributes: ['id', 'title', 'price', 'year', 'images'],
          },
          {
            model: User,
            as: 'Seller',
            attributes: ['id', 'username', 'full_name'],
          },
        ],
        order: [['created_at', 'DESC']],
        ...option?.queryInfo,
      })
    );

    return {
      rows: inquiries,
      count: inquiries.length,
    };
  }

  /**
   * Get all inquiries received by a user (seller)
   */
  async getReceivedInquiries(userId: string, option?: ICrudOption) {
    const inquiries = await this.exec(
      this.model.findAll({
        where: {
          seller_id: userId,
          status: true,
        },
        include: [
          {
            model: UsedCarPost,
            as: 'UsedCarPost',
            where: { status: true },
            required: true,
            attributes: ['id', 'title', 'price', 'year', 'images'],
          },
          {
            model: User,
            as: 'Buyer',
            attributes: ['id', 'username', 'full_name'],
          },
        ],
        order: [['created_at', 'DESC']],
        ...option?.queryInfo,
      })
    );

    return {
      rows: inquiries,
      count: inquiries.length,
    };
  }

  /**
   * Create a new inquiry
   */
  async createInquiry(params: any, option?: ICrudOption) {
    // Validate that the post exists and get seller info
    const post = await this.exec(
      UsedCarPost.findOne({
        where: {
          id: params.used_car_post_id,
          status: true,
        },
        attributes: ['id', 'user_id'],
      }),
      {
        allowNull: false,
      }
    );

    // Set seller_id from the post
    params.seller_id = post.user_id;

    // Prevent self-inquiry
    if (params.buyer_id === params.seller_id) {
      throw new Error('You cannot inquire about your own post');
    }

    const inquiry = await this.exec(
      this.model.create(params as any)
    );

    return inquiry;
  }

  /**
   * Update inquiry status (for seller response)
   */
  async updateInquiryStatus(inquiryId: string, userId: string, params: any, option?: ICrudOption) {
    const inquiry = await this.exec(
      this.model.findOne({
        where: {
          id: inquiryId,
          seller_id: userId, // Only seller can update
          status: true,
        },
      }),
      {
        allowNull: false,
      }
    );

    const updatedInquiry = await this.exec(
      inquiry.update(params)
    );

    return updatedInquiry;
  }

  /**
   * Mark inquiry as read
   */
  async markAsRead(inquiryId: string, userId: string, userType: 'buyer' | 'seller', option?: ICrudOption) {
    const whereClause: any = {
      id: inquiryId,
      status: true,
    };

    if (userType === 'buyer') {
      whereClause.buyer_id = userId;
    } else {
      whereClause.seller_id = userId;
    }

    const inquiry = await this.exec(
      this.model.findOne({
        where: whereClause,
      }),
      {
        allowNull: false,
      }
    );

    const updateData: any = {};
    if (userType === 'buyer') {
      updateData.is_read_by_buyer = true;
    } else {
      updateData.is_read_by_seller = true;
    }

    const updatedInquiry = await this.exec(
      inquiry.update(updateData)
    );

    return updatedInquiry;
  }

  /**
   * Get inquiry details
   */
  async getInquiryDetails(inquiryId: string, userId: string, option?: ICrudOption) {
    const inquiry = await this.exec(
      this.model.findOne({
        where: {
          id: inquiryId,
          [Op.or]: [
            { buyer_id: userId },
            { seller_id: userId },
          ],
          status: true,
        },
        include: [
          {
            model: UsedCarPost,
            as: 'UsedCarPost',
            where: { status: true },
            required: true,
          },
          {
            model: User,
            as: 'Buyer',
            attributes: ['id', 'username', 'full_name'],
          },
          {
            model: User,
            as: 'Seller',
            attributes: ['id', 'username', 'full_name'],
          },
        ],
      }),
      {
        allowNull: false,
      }
    );

    return inquiry;
  }
}

export const usedCarInquiryService = new UsedCarInquiryService(); 