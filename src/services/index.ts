import {ErrorService} from './errorService'
import {TokenService} from './tokenService'
import {UtilService} from './utilService'
import {FirebaseService} from './firebaseService'
// Crud
import {ICrudExecOption, ICrudOption, CrudService} from './crudService'
import {ScheduleService} from './scheduleService'
import {EmployeeService} from './crud/employeeService'
import {PushNotificationService} from './crud/pushNotificationService'
import {UserService} from './crud/userService'
import {CityService} from './crud/cityService'
import {DistrictService} from './crud/districtService'
import {EventService} from './crud/eventService'
import {PostService} from './crud/postService'
// import { CommentService } from './crud/commentService'

// import { KeywordService } from './crud/keywordService'
// import { SampleCrudService } from './crud/sampleCrudService.mongo'
import {SettingService} from './crud/settingService'
import {CategoryService} from './crud/categoryService'
import {TagService} from './crud/tagService'
import {ThemaService} from './crud/themaService'
import {CommentService} from './crud/commentService'
import {ShopService} from './crud/shopService'
import {ShopTagService} from './crud/shopTagService'
import {ReviewService} from './crud/reviewService'
import {FavouriteService} from './crud/favouriteService'
import {WardService} from './crud/wardService'
import {FavouritePostService} from './crud/favouritePostService'
import {LinkService} from './crud/linkService'
import {LinkCategoryService} from './crud/linkCategoryService'
import {DislikePostService} from './crud/dislikePostService'
import {SettingUserPermissionService} from './crud/settingUserPermissionService'
import {BannerService} from './crud/bannerService'
import {NotificationService} from './crud/notificationService'
import {RecentReadingService} from './crud/recentReadingService'
import {ContactService} from './crud/contactService'
import {ContentService} from './crud/contentService'
import {StatisticService} from './crud/statisticService'
import {RecordService} from './crud/recordService'
import {SeoService} from './crud/seoService'
import {MetaService} from './crud/metaService'
import {RecruitService} from './crud/recruitService'
import {KLocationService} from './crud/klocationService'
import {HistoryService} from './crud/historyService'
import {ReportService} from './crud/reportService'
import {FaqService} from './crud/faqService'
import {FaqCategoryService} from './crud/faqCategoryService'
import {CourseService} from './crud/courseService'
import {PriceService} from './crud/priceService'
import {ReservationService} from './crud/reservationService'
import {ReservationItemService} from './crud/reservationItemService'
import {MentorService} from './crud/mentorService'
import {FavouriteMentorService} from './crud/favouriteMentorService'
import {LoyaltyService} from './crud/loyaltyService'
import {AdminSettingService} from './crud/adminSettingService'
import {BlockService} from './crud/blockService'
import {ShortVideoService} from './crud/shortVideoService'
import {FavouriteShortVideoService} from './crud/favouriteShortVideoService'
import {QuestionService} from './crud/questionService'
import {AnswerQuestionService} from './crud/answerQuestionService'
import {UserPaymentHistoryService} from './crud/userPaymentHistoryService'
import {TicketUsedService} from './crud/ticketUsedService'
import {AppVersionService} from './crud/appVersionService'
import {ConversationService} from './crud/conversationService'
import {MessageService} from './crud/messageService'
import {PointProductService} from './crud/pointProductService'
import {PointService} from './crud/pointService'
import {PointProductHistoryService} from './crud/pointProductHistoryService'
import {FeedbackService} from './crud/feedbackService'
import {FeedbackItemService} from './crud/feedbackItemService'
import {SeoSsrService} from './crud/seoSsrService'
import {BlogService} from './crud/blogService'
import {GroupService} from "@/services/crud/groupService";
import {SmsService} from "@/services/smsService";
import {NavIconService} from "@/services/crud/navIconService";
import {NavbarService} from "@/services/crud/navbarService";
import {SettingStationSubwayService} from "@/services/crud/settingStationSubwayService";
import {SettingProvinceService} from "@/services/crud/settingProvinceService";
import {SettingDistrictService} from "@/services/crud/settingDistrictService";
import {SettingStationService} from "@/services/crud/settingStationService";
import {SettingStationLineService} from "@/services/crud/settingStationLineService";
import {SiteService} from "@/services/crud/siteService";
import {SiteCategoryService} from "@/services/crud/siteCategoryService";
import {RealEstateService} from "@/services/crud/realEstateService";
import {RealEstateTagService} from "@/services/crud/realEstateTagService";
import {SecondHandMarketService} from "@/services/crud/secondHandMarketService";
import {PopupService} from "@/services/crud/popupService";
import {KeywordCategoryService} from "@/services/crud/keywordCategoryService";
import {KeywordService} from "@/services/crud/keywordService";
import {KeywordTypeService} from "@/services/crud/keywordTypeService";
import { SiteBannerService } from "@/services/crud/siteBanner";
import { JobRequestService } from '@/services/crud/jobRequestService';
import { QuoteService } from '@/services/crud/quoteService';
import { FavoriteJobService } from '@/services/crud/favoriteJobService';
import { ExpertInfoService } from '@/services/crud/expertInfoService';
import { AdvertisingImageService } from '@/services/crud/advertisingImageService';
import { ActionLogService } from '@/services/crud/actionLogService'
import { BoardPermissionService } from '@/services/crud/boardPermission'
import { BoardService } from '@/services/crud/boardService'
import { ExpRuleService } from '@/services/crud/expRuleService'
import { LevelService } from '@/services/crud/levelService'
import { RankingService } from '@/services/crud/rankingService'
import { VideoService } from '@/services/crud/videoService'
import { RewardService } from '@/services/crud/rewardService'
import { RankingDetailService } from '@/services/crud/rankingDetailService'
import { PopupWindowService } from '@/services/crud/popupWindowService'
import { UsedCarPostService } from '@/services/crud/usedCarPostService'
import { UsedCarSavedSearchService } from '@/services/crud/usedCarSavedSearchService'
import { FavouriteUsedCarPostService } from '@/services/crud/favouriteUsedCarPostService'
import { UsedCarInquiryService } from '@/services/crud/usedCarInquiryService'
import { CarManufacturerService } from '@/services/crud/carManufacturerService'
import { CarModelService } from '@/services/crud/carModelService'
import { CarOptionGroupService } from '@/services/crud/carOptionGroupService'
import { CarOptionService } from '@/services/crud/carOptionService'

const errorService = new ErrorService()
const tokenService = new TokenService()
const utilService = new UtilService()
const scheduleService = new ScheduleService()
const firebaseService = new FirebaseService()
const smsService = new SmsService()
const shopService = new ShopService()
// const keywordService = new KeywordService()
// Crud
// const adminService = new AdminService()
const employeeService = new EmployeeService()
const pushNotificationService = new PushNotificationService()
const userService = new UserService()
const cityService = new CityService()
const districtService = new DistrictService()
const settingService = new SettingService()
const categoryService = new CategoryService()
const tagService = new TagService()
const themaService = new ThemaService()
const postService = new PostService()
const commentService = new CommentService()
const eventService = new EventService()
const shopTagService = new ShopTagService()
const reviewService = new ReviewService()
const favouriteService = new FavouriteService()
const wardService = new WardService()
const favouritePostService = new FavouritePostService()
const linkService = new LinkService()
const linkCategoryService = new LinkCategoryService()
const dislikePostService = new DislikePostService()
const settingUserPermissionService = new SettingUserPermissionService()
const bannerService = new BannerService()
const notificationService = new NotificationService()
const recentReadingService = new RecentReadingService()
const contactService = new ContactService()
const contentService = new ContentService()
const statisticService = new StatisticService()
const recordService = new RecordService()
const seoService = new SeoService()
const metaService = new MetaService()
const recruitService = new RecruitService()
const kLocationService = new KLocationService()
const historyService = new HistoryService()
const reportService = new ReportService()
const faqService = new FaqService()
const faqCategoryService = new FaqCategoryService()
const courseService = new CourseService()
const priceService = new PriceService()
const reservationService = new ReservationService()
const reservationItemService = new ReservationItemService()
const mentorService = new MentorService()
const favouriteMentorService = new FavouriteMentorService()
const loyaltyService = new LoyaltyService()
const adminSettingService = new AdminSettingService()
const blockService = new BlockService()
const shortVideoService = new ShortVideoService()
const favouriteShortVideoService = new FavouriteShortVideoService()
const questionService = new QuestionService()
const answerQuestionService = new AnswerQuestionService()
const userPaymentHistoryService = new UserPaymentHistoryService()
const ticketUsedService = new TicketUsedService()
const appVersionService = new AppVersionService()
const conversationService = new ConversationService()
const messageService = new MessageService()
const pointProductService = new PointProductService()
const pointService = new PointService()
const pointProductHistoryService = new PointProductHistoryService()
const feedbackService = new FeedbackService()
const feedbackItemService = new FeedbackItemService()
const seoSsrService = new SeoSsrService()
const blogService = new BlogService()
const groupService = new GroupService()
const navIconService = new NavIconService()
const navbarService = new NavbarService()
const settingStationSubwayService = new SettingStationSubwayService()
const settingProvinceService = new SettingProvinceService()
const settingDistrictService = new SettingDistrictService()
const settingStationService = new SettingStationService()
const settingStationLineService = new SettingStationLineService()

const siteService = new SiteService()
const siteCategoryService = new SiteCategoryService()
const realEstateService = new RealEstateService()

const realEstateTagService = new RealEstateTagService()

const secondHandMarketService = new SecondHandMarketService()
const popupService = new PopupService()
const carManufacturerService = new CarManufacturerService()
const carModelService = new CarModelService()
const carOptionGroupService = new CarOptionGroupService()
const carOptionService = new CarOptionService()

const keywordService = new KeywordService()

const keywordCategoryService = new KeywordCategoryService()

const keywordTypeService = new KeywordTypeService()

const siteBannerService = new SiteBannerService()

const jobRequestService = new JobRequestService()

const quoteService = new QuoteService()

const favoriteJobService = new FavoriteJobService()

const expertInfoService = new ExpertInfoService()

const advertisingImageService = new AdvertisingImageService()

const actionLogService = new ActionLogService()

const boardPermissionService = new BoardPermissionService()

const boardService = new BoardService()

const expRuleService = new ExpRuleService()

const levelService = new LevelService()

const rankingService = new RankingService()

const videoService = new VideoService()

const rewardService = new RewardService()

const rankingDetailService = new RankingDetailService()

const popupWindowService = new PopupWindowService()

const usedCarPostService = new UsedCarPostService()
const usedCarSavedSearchService = new UsedCarSavedSearchService()
const favouriteUsedCarPostService = new FavouriteUsedCarPostService()
const usedCarInquiryService = new UsedCarInquiryService()
export {
    errorService,
    tokenService,
    utilService,
    scheduleService,
    cityService,
    districtService,
    wardService,
    tagService,
    settingService,
    categoryService,
    themaService,
    postService,
    commentService,
    employeeService,
    userService,
    shopService,
    eventService,
    shopTagService,
    reviewService,
    favouriteService,
    favouritePostService,
    linkService,
    linkCategoryService,
    dislikePostService,
    settingUserPermissionService,
    bannerService,
    notificationService,
    recentReadingService,
    contactService,
    contentService,
    statisticService,
    recordService,
    seoService,
    metaService,
    recruitService,
    kLocationService,
    historyService,
    reportService,
    faqService,
    faqCategoryService,
    courseService,
    priceService,
    reservationService,
    reservationItemService,
    mentorService,
    favouriteMentorService,
    loyaltyService,
    adminSettingService,
    blockService,
    shortVideoService,
    questionService,
    answerQuestionService,
    userPaymentHistoryService,
    ticketUsedService,
    appVersionService,
    conversationService,
    messageService,
    pointProductService,
    pointService,
    //   eventService,
    firebaseService,
    favouriteShortVideoService,
    pushNotificationService,
    pointProductHistoryService,
    feedbackService,
    feedbackItemService,
    seoSsrService,
    groupService,
    blogService,
    CrudService,
    ICrudExecOption,
    ICrudOption,
    smsService,
    navIconService,
    navbarService,
    settingProvinceService,
    settingDistrictService,
    settingStationService,
    settingStationLineService,
    settingStationSubwayService,
    siteService,
    siteCategoryService,
    realEstateService,
    realEstateTagService,
    secondHandMarketService,
    popupService,
    carManufacturerService,
    carModelService,
    carOptionGroupService,
    carOptionService,
    keywordCategoryService,
    keywordService,
    keywordTypeService,
    siteBannerService,
    jobRequestService,
    quoteService,
    favoriteJobService,
    expertInfoService,
    advertisingImageService,
    actionLogService,
    boardPermissionService,
    boardService,
    expRuleService,
    levelService,
    rankingService,
    videoService,
    rewardService,
    rankingDetailService,
    popupWindowService,
    usedCarPostService,
    usedCarSavedSearchService,
    favouriteUsedCarPostService,
    usedCarInquiryService
}
