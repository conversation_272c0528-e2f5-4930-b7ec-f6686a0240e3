import { <PERSON><PERSON><PERSON>outer } from '../crud';
import { carOptionGroupController } from '@/controllers';
import { Request, Response } from '../base';
import {
    authInfoMiddleware,
    adminTypeMiddleware,
    queryMiddleware
} from '@/middlewares';

export default class Car<PERSON><PERSON>GroupRouter extends <PERSON><PERSON><PERSON>outer<typeof carOptionGroupController> {
    constructor() {
        super(carOptionGroupController);
    }

    // Override middleware methods to require admin authentication
    getListMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run(), queryMiddleware.run()];
    }

    getItemMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run(), queryMiddleware.run()];
    }

    createMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    updateMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    deleteAllMiddlewares(): any[] {
        return [authInfoMiddleware.run(), adminTypeMiddleware.run()];
    }

    // Override create with input validation
    async create(req: Request, res: Response) {
        await this.validateJSON(req.body, {
            type: 'object',
            properties: {
                name: {
                    type: 'string',
                    minLength: 1,
                    maxLength: 100
                },
                description: {
                    type: 'string',
                    maxLength: 500
                },
                icon: {
                    type: 'string',
                    maxLength: 50
                },
                color: {
                    type: 'string',
                    pattern: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$'
                },
                index: {
                    type: 'integer',
                    minimum: 0,
                    maximum: 999
                }
            },
            required: ['name'],
            additionalProperties: false
        });

        const result = await this.controller.create(req.body);
        this.onSuccess(res, result);
    }

    // Override update with input validation
    async update(req: Request, res: Response) {
        const { id } = req.params;

        // Validate UUID format
        if (!this.isValidUUID(id)) {
            throw this.createValidationError('Invalid option group ID format');
        }

        await this.validateJSON(req.body, {
            type: 'object',
            properties: {
                name: {
                    type: 'string',
                    minLength: 1,
                    maxLength: 100
                },
                description: {
                    type: 'string',
                    maxLength: 500
                },
                icon: {
                    type: 'string',
                    maxLength: 50
                },
                color: {
                    type: 'string',
                    pattern: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$'
                },
                index: {
                    type: 'integer',
                    minimum: 0,
                    maximum: 999
                }
            },
            additionalProperties: false
        });

        const result = await this.controller.update(req.body, {
            filter: { id }
        });
        this.onSuccess(res, result);
    }

    // Override getItem to handle queryInfo initialization and validation
    async getItem(req: Request, res: Response) {
        const { id } = req.params;

        // Validate UUID format
        if (!this.isValidUUID(id)) {
            throw this.createValidationError('Invalid option group ID format');
        }

        // Initialize queryInfo if it's undefined (fallback for middleware issues)
        if (!req.queryInfo) {
            req.queryInfo = {
                filter: {},
                limit: 20,
                page: 1,
                offset: 0,
                order: [['updated_at', 'desc']]
            };
        }

        req.queryInfo.filter.id = id;
        const result = await this.controller.getItem(req.queryInfo);
        this.onSuccess(res, result);
    }

    // Override delete with validation
    async delete(req: Request, res: Response) {
        const { id } = req.params;

        // Validate UUID format
        if (!this.isValidUUID(id)) {
            throw this.createValidationError('Invalid option group ID format');
        }

        const result = await this.controller.delete({
            filter: { id }
        });
        this.onSuccess(res, result);
    }

    // Helper method to validate UUID format
    private isValidUUID(uuid: string): boolean {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        return uuidRegex.test(uuid);
    }

    // Helper method to create validation errors
    private createValidationError(message: string) {
        return {
            options: {
                code: 400,
                type: 'validation_error',
                message
            }
        };
    }
}
