import { CrudService } from '../crudService';
import { ICrudOption } from '../';
import { FavouriteUsedCarPost, UsedCarPost, User } from '@/models';

export class FavouriteUsedCarPostService extends CrudService<typeof FavouriteUsedCarPost> {
  constructor() {
    super(FavouriteUsedCarPost);
  }

  /**
   * Get all favorite posts for a user
   */
  async getUserFavorites(userId: string, option?: ICrudOption) {
    const favorites = await this.exec(
      this.model.findAll({
        where: {
          user_id: userId,
          status: true,
        },
        include: [
          {
            model: UsedCarPost,
            as: 'UsedCarPost',
            where: { status: true },
            required: true,
          },
        ],
        order: [['created_at', 'DESC']],
        ...option?.queryInfo,
      })
    );

    return {
      rows: favorites,
      count: favorites.length,
    };
  }

  /**
   * Add a post to user's favorites
   */
  async addToFavorites(userId: string, postId: string, option?: ICrudOption) {
    // Check if post exists
    const post = await this.exec(
      UsedCarPost.findOne({
        where: {
          id: postId,
          status: true,
        },
      }),
      {
        allowNull: false,
      }
    );

    // Check if already favorited
    const existingFavorite = await this.exec(
      this.model.findOne({
        where: {
          user_id: userId,
          used_car_post_id: postId,
          status: true,
        },
      })
    );

    if (existingFavorite) {
      throw new Error('Post is already in favorites');
    }

    const favorite = await this.exec(
      this.model.create({
        user_id: userId,
        used_car_post_id: postId,
      } as any)
    );

    return favorite;
  }

  /**
   * Remove a post from user's favorites
   */
  async removeFromFavorites(userId: string, postId: string, option?: ICrudOption) {
    const favorite = await this.exec(
      this.model.findOne({
        where: {
          user_id: userId,
          used_car_post_id: postId,
          status: true,
        },
      }),
      {
        allowNull: false,
      }
    );

    await this.exec(
      this.model.destroy({
        where: {
          user_id: userId,
          used_car_post_id: postId,
        },
      })
    );

    return { success: true, message: 'Post removed from favorites successfully' };
  }

  /**
   * Check if a post is favorited by user
   */
  async isFavorited(userId: string, postId: string, option?: ICrudOption) {
    const favorite = await this.exec(
      this.model.findOne({
        where: {
          user_id: userId,
          used_car_post_id: postId,
          status: true,
        },
      })
    );

    return !!favorite;
  }
}

export const favouriteUsedCarPostService = new FavouriteUsedCarPostService(); 