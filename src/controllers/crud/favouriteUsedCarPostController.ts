import { <PERSON><PERSON><PERSON>ontroller } from '../crudController';
import { ICrudOption, favouriteUsedCarPostService } from '@/services';

/**
 * @swagger
 * tags:
 *   name: Favorite Posts
 *   description: User favorite used car posts management
 */

export class FavouriteUsedCarPostController extends Crud<PERSON>ontroller<typeof favouriteUsedCarPostService> {
  constructor() {
    super(favouriteUsedCarPostService);
  }

  /**
   * @swagger
   * /favorite-posts:
   *   get:
   *     summary: Get all favorite posts for the authenticated user
   *     tags: [Favorite Posts]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: List of favorite posts
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/FavouriteUsedCarPost'
   *                 count:
   *                   type: integer
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async getUserFavorites(userId: string, option?: ICrudOption) {
    return await this.service.getUserFavorites(userId, option);
  }

  /**
   * @swagger
   * /favorite-posts:
   *   post:
   *     summary: Add a post to user's favorites
   *     tags: [Favorite Posts]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - used_car_post_id
   *             properties:
   *               used_car_post_id:
   *                 type: string
   *                 format: uuid
   *                 description: ID of the used car post to favorite
   *     responses:
   *       201:
   *         description: Post added to favorites successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/FavouriteUsedCarPost'
   *       400:
   *         description: Invalid input data or post already favorited
   *       404:
   *         description: Post not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async addToFavorites(userId: string, postId: string, option?: ICrudOption) {
    return await this.service.addToFavorites(userId, postId, option);
  }

  /**
   * @swagger
   * /favorite-posts/{postId}:
   *   delete:
   *     summary: Remove a post from user's favorites
   *     tags: [Favorite Posts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: postId
   *         required: true
   *         schema:
   *           type: string
   *         description: Used car post ID to remove from favorites
   *     responses:
   *       200:
   *         description: Post removed from favorites successfully
   *       404:
   *         description: Favorite not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async removeFromFavorites(userId: string, postId: string, option?: ICrudOption) {
    return await this.service.removeFromFavorites(userId, postId, option);
  }

  /**
   * @swagger
   * /favorite-posts/{postId}/check:
   *   get:
   *     summary: Check if a post is favorited by the user
   *     tags: [Favorite Posts]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: postId
   *         required: true
   *         schema:
   *           type: string
   *         description: Used car post ID to check
   *     responses:
   *       200:
   *         description: Favorite status
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 is_favorited:
   *                   type: boolean
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async isFavorited(userId: string, postId: string, option?: ICrudOption) {
    const result = await this.service.isFavorited(userId, postId, option);
    return { is_favorited: result };
  }
}

export const favouriteUsedCarPostController = new FavouriteUsedCarPostController(); 