import { <PERSON><PERSON><PERSON>outer } from '../crud';
import { Request, Response } from '../base';
import { favouriteUsedCarPostController } from '@/controllers/crud/favouriteUsedCarPostController';
import { authInfoMiddleware, queryMiddleware } from '@/middlewares';

export default class FavoritePostsRouter extends CrudRouter<typeof favouriteUsedCarPostController> {
  constructor() {
    super(favouriteUsedCarPostController);
  }

  customRouting() {
    // GET /api/v1/favorite-posts - Get user's favorite posts
    this.router.get(
      '/',
      [authInfoMiddleware.run(), queryMiddleware.run()],
      this.route(this.getUserFavorites)
    );

    // POST /api/v1/favorite-posts - Add a post to favorites
    this.router.post(
      '/',
      [authInfoMiddleware.run()],
      this.route(this.addToFavorites)
    );

    // DELETE /api/v1/favorite-posts/:postId - Remove a post from favorites
    this.router.delete(
      '/:postId',
      [authInfoMiddleware.run()],
      this.route(this.removeFromFavorites)
    );

    // GET /api/v1/favorite-posts/:postId/check - Check if post is favorited
    this.router.get(
      '/:postId/check',
      [authInfoMiddleware.run()],
      this.route(this.checkFavoriteStatus)
    );
  }

  async getUserFavorites(req: Request, res: Response) {
    const userId = req.tokenInfo.payload.user_id;
    const result = await this.controller.getUserFavorites(userId, {
      queryInfo: req.queryInfo,
    });
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }

  async addToFavorites(req: Request, res: Response) {
    const userId = req.tokenInfo.payload.user_id;
    
    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
        used_car_post_id: {
          type: 'string',
          format: 'uuid'
        }
      },
      required: ['used_car_post_id']
    });

    const { used_car_post_id } = req.body;
    const result = await this.controller.addToFavorites(userId, used_car_post_id);
    this.onSuccess(res, result);
  }

  async removeFromFavorites(req: Request, res: Response) {
    const userId = req.tokenInfo.payload.user_id;
    const postId = req.params.postId;
    const result = await this.controller.removeFromFavorites(userId, postId);
    this.onSuccess(res, result);
  }

  async checkFavoriteStatus(req: Request, res: Response) {
    const userId = req.tokenInfo.payload.user_id;
    const postId = req.params.postId;
    const result = await this.controller.isFavorited(userId, postId);
    this.onSuccess(res, result);
  }
} 