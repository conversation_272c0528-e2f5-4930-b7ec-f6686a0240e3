import { CrudService, ICrudOption } from '../crudService.pg';
import { CarModel, CarManufacturer } from '@/models';
import { config } from '@/config';
import { errorService } from '@/services';

export class CarModelService extends CrudService<typeof CarModel> {
  constructor() {
    super(CarModel);
  }

  async getList(
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ['defaultScope'],
    }
  ) {
    return await this.exec(
      this.modelWithScope(option.scope).findAll({
        include: [
          {
            association: 'manufacturer',
            attributes: ['id', 'name', 'country']
          }
        ],
        limit: option.limit,
        offset: option.offset,
        order: [['created_at', 'DESC']],
        where: option.filter || {}
      })
    );
  }

  async getItem(option: ICrudOption) {
    return await this.exec(
      this.modelWithScope(option.scope).findOne({
        include: [
          {
            association: 'manufacturer',
            attributes: ['id', 'name', 'country']
          }
        ],
        where: option.filter
      })
    );
  }

  /**
   * Enhanced create method with relationship validation
   */
  async create(params: any, option?: ICrudOption) {
    // Validate required fields
    if (!params.name || !params.name.trim()) {
      throw errorService.database.queryFail('Car model name is required', 400);
    }

    if (!params.manufacturer_id) {
      throw errorService.database.queryFail('Manufacturer ID is required', 400);
    }

    // Validate manufacturer exists
    const manufacturer = await this.exec(
      CarManufacturer.findByPk(params.manufacturer_id),
      { allowNull: true }
    );

    if (!manufacturer) {
      throw errorService.database.queryFail('Invalid manufacturer ID: manufacturer does not exist', 400);
    }

    // Check for duplicate model name within the same manufacturer
    const existingModel = await this.exec(
      CarModel.findOne({
        where: {
          manufacturer_id: params.manufacturer_id,
          name: {
            [require('sequelize').Op.iLike]: params.name.trim()
          }
        }
      }),
      { allowNull: true }
    );

    if (existingModel) {
      throw errorService.database.queryFail(
        'A model with this name already exists for this manufacturer', 
        409
      );
    }

    // Validate year ranges if provided
    if (params.start_year && params.end_year) {
      if (params.start_year > params.end_year) {
        throw errorService.database.queryFail('Start year cannot be greater than end year', 400);
      }
    }

    const currentYear = new Date().getFullYear();
    if (params.start_year && (params.start_year < 1900 || params.start_year > currentYear + 5)) {
      throw errorService.database.queryFail('Start year must be between 1900 and ' + (currentYear + 5), 400);
    }

    if (params.end_year && (params.end_year < 1900 || params.end_year > currentYear + 5)) {
      throw errorService.database.queryFail('End year must be between 1900 and ' + (currentYear + 5), 400);
    }

    return await super.create(params, option);
  }

  /**
   * Enhanced update method with relationship validation
   */
  async update(params: any, option?: ICrudOption) {
    // Validate name if provided
    if (params.name && !params.name.trim()) {
      throw errorService.database.queryFail('Car model name cannot be empty', 400);
    }

    // Validate manufacturer if being updated
    if (params.manufacturer_id) {
      const manufacturer = await this.exec(
        CarManufacturer.findByPk(params.manufacturer_id),
        { allowNull: true }
      );

      if (!manufacturer) {
        throw errorService.database.queryFail('Invalid manufacturer ID: manufacturer does not exist', 400);
      }
    }

    // Check for duplicate model name if name or manufacturer is being updated
    if (params.name || params.manufacturer_id) {
      const currentModel = await this.getItem(option);
      const manufacturerId = params.manufacturer_id || currentModel.manufacturer_id;
      const modelName = params.name || currentModel.name;

      const existingModel = await this.exec(
        CarModel.findOne({
          where: {
            manufacturer_id: manufacturerId,
            name: {
              [require('sequelize').Op.iLike]: modelName.trim()
            },
            id: {
              [require('sequelize').Op.ne]: option.filter.id
            }
          }
        }),
        { allowNull: true }
      );

      if (existingModel) {
        throw errorService.database.queryFail(
          'A model with this name already exists for this manufacturer', 
          409
        );
      }
    }

    // Validate year ranges if being updated
    const currentModel = await this.getItem(option);
    const startYear = params.start_year !== undefined ? params.start_year : currentModel.start_year;
    const endYear = params.end_year !== undefined ? params.end_year : currentModel.end_year;

    if (startYear && endYear && startYear > endYear) {
      throw errorService.database.queryFail('Start year cannot be greater than end year', 400);
    }

    const currentYear = new Date().getFullYear();
    if (params.start_year !== undefined && params.start_year && 
      (params.start_year < 1900 || params.start_year > currentYear + 5)) {
      throw errorService.database.queryFail('Start year must be between 1900 and ' + (currentYear + 5), 400);
    }

    if (params.end_year !== undefined && params.end_year && 
      (params.end_year < 1900 || params.end_year > currentYear + 5)) {
      throw errorService.database.queryFail('End year must be between 1900 and ' + (currentYear + 5), 400);
    }

    return await super.update(params, option);
  }

  /**
   * Get models with manufacturer information
   */
  async getModelsWithManufacturer(manufacturerId?: string) {
    const whereClause = manufacturerId ? { manufacturer_id: manufacturerId } : {};

    return await this.exec(
      CarModel.findAll({
        where: whereClause,
        include: [{
          model: CarManufacturer,
          as: 'manufacturer',
          attributes: ['id', 'name', 'country']
        }],
        order: [['name', 'ASC']]
      })
    );
  }
}
