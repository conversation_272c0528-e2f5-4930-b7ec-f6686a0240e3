import { <PERSON><PERSON>Router } from '../crud';
import { Request, Response } from '../base';
import { usedCarSavedSearchController } from '@/controllers/crud/usedCarSavedSearchController';
import { authInfoMiddleware, queryMiddleware } from '@/middlewares';

export default class SavedSearchesRouter extends CrudRouter<typeof usedCarSavedSearchController> {
  constructor() {
    super(usedCarSavedSearchController);
  }

  customRouting() {
    // GET /api/v1/saved-searches - Get user's saved searches
    this.router.get(
      '/',
      [authInfoMiddleware.run(), queryMiddleware.run()],
      this.route(this.getUserSavedSearches)
    );

    // POST /api/v1/saved-searches - Create a new saved search
    this.router.post(
      '/',
      [authInfoMiddleware.run()],
      this.route(this.createSavedSearch)
    );

    // PUT /api/v1/saved-searches/:id - Update a saved search
    this.router.put(
      '/:id',
      [authInfoMiddleware.run()],
      this.route(this.updateSavedSearch)
    );

    // DELETE /api/v1/saved-searches/:id - Delete a saved search
    this.router.delete(
      '/:id',
      [authInfoMiddleware.run()],
      this.route(this.deleteSavedSearch)
    );
  }

  async getUserSavedSearches(req: Request, res: Response) {
    const userId = req.tokenInfo.payload.user_id;
    const result = await this.controller.getUserSavedSearches(userId, {
      queryInfo: req.queryInfo,
    });
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }

  async createSavedSearch(req: Request, res: Response) {
    const userId = req.tokenInfo.payload.user_id;
    const params = {
      ...req.body,
      user_id: userId,
    };
    
    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          minLength: 1,
          maxLength: 100
        },
        manufacturer_id: {
          type: 'string',
          format: 'uuid'
        },
        model_id: {
          type: 'string',
          format: 'uuid'
        },
        category_id: {
          type: 'string',
          format: 'uuid'
        },
        price_min: {
          type: 'number',
          minimum: 0
        },
        price_max: {
          type: 'number',
          minimum: 0
        },
        year_min: {
          type: 'integer',
          minimum: 1900
        },
        year_max: {
          type: 'integer',
          maximum: new Date().getFullYear() + 1
        },
        mileage_min: {
          type: 'integer',
          minimum: 0
        },
        mileage_max: {
          type: 'integer',
          minimum: 0
        },
        fuel_type: {
          type: 'string',
          enum: ['gasoline', 'diesel', 'hybrid', 'electric', 'lpg']
        },
        transmission: {
          type: 'string',
          enum: ['manual', 'automatic', 'cvt']
        },
        body_type: {
          type: 'string',
          enum: ['sedan', 'hatchback', 'suv', 'coupe', 'convertible', 'wagon', 'pickup', 'van', 'minivan']
        },
        condition: {
          type: 'string',
          enum: ['excellent', 'good', 'fair', 'poor']
        },
        location_radius: {
          type: 'number',
          minimum: 0
        },
        location_latitude: {
          type: 'number',
          minimum: -90,
          maximum: 90
        },
        location_longitude: {
          type: 'number',
          minimum: -180,
          maximum: 180
        },
        keywords: {
          type: 'string'
        },
        is_negotiable: {
          type: 'boolean'
        },
        notification_enabled: {
          type: 'boolean'
        }
      },
      required: ['name']
    });

    const result = await this.controller.createSavedSearch(params);
    this.onSuccess(res, result);
  }

  async updateSavedSearch(req: Request, res: Response) {
    const userId = req.tokenInfo.payload.user_id;
    const searchId = req.params.id;
    const result = await this.controller.updateSavedSearch(
      searchId,
      userId,
      req.body
    );
    this.onSuccess(res, result);
  }

  async deleteSavedSearch(req: Request, res: Response) {
    const userId = req.tokenInfo.payload.user_id;
    const searchId = req.params.id;
    const result = await this.controller.deleteSavedSearch(searchId, userId);
    this.onSuccess(res, result);
  }
} 