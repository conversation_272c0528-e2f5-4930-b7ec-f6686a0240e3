import { <PERSON>rud<PERSON>ontroller } from '../crudController';
import { carManufacturerService } from '@/services';

/**
 * @swagger
 * tags:
 *   name: Car Manufacturers
 *   description: Car manufacturer management and operations (Admin only)
 */
export class CarManufacturerController extends CrudController<typeof carManufacturerService> {
  constructor() {
    super(carManufacturerService);
  }

  /**
   * @swagger
   * /car-manufacturers:
   *   get:
   *     summary: Get list of car manufacturers
   *     description: Retrieve a paginated list of car manufacturers. Admin authentication required.
   *     tags: [Car Manufacturers]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PaginationLimit'
   *       - $ref: '#/components/parameters/PaginationOffset'
   *       - $ref: '#/components/parameters/FilterParam'
   *       - $ref: '#/components/parameters/SortParam'
   *       - $ref: '#/components/parameters/SearchParam'
   *       - in: query
   *         name: status
   *         schema:
   *           type: boolean
   *         description: Filter by active status
   *       - in: query
   *         name: country
   *         schema:
   *           type: string
   *         description: Filter by country
   *     responses:
   *       200:
   *         description: List of car manufacturers retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/CarManufacturer'
   *                 count:
   *                   type: integer
   *                   description: Total number of manufacturers
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /car-manufacturers/{id}:
   *   get:
   *     summary: Get a specific car manufacturer
   *     description: Retrieve details of a specific car manufacturer by ID. Admin authentication required.
   *     tags: [Car Manufacturers]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Car manufacturer ID
   *     responses:
   *       200:
   *         description: Car manufacturer details retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/CarManufacturer'
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /car-manufacturers:
   *   post:
   *     summary: Create a new car manufacturer
   *     description: Create a new car manufacturer entry. Admin authentication required.
   *     tags: [Car Manufacturers]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *               - country
   *             properties:
   *               name:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 100
   *                 description: Manufacturer name (e.g., Toyota, Honda, BMW)
   *                 example: "Toyota"
   *               country:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 50
   *                 description: Country of origin
   *                 example: "Japan"
   *               logo_url:
   *                 type: string
   *                 maxLength: 500
   *                 description: URL to manufacturer logo
   *                 example: "https://example.com/logos/toyota.png"
   *               website_url:
   *                 type: string
   *                 maxLength: 500
   *                 description: Official website URL
   *                 example: "https://toyota.com"
   *               description:
   *                 type: string
   *                 maxLength: 1000
   *                 description: Brief description of the manufacturer
   *                 example: "Japanese multinational automotive manufacturer"
   *             additionalProperties: false
   *     responses:
   *       201:
   *         description: Car manufacturer created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/CarManufacturer'
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       409:
   *         description: Manufacturer with this name already exists
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "Manufacturer name already exists"
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /car-manufacturers/{id}:
   *   put:
   *     summary: Update a car manufacturer
   *     description: Update an existing car manufacturer. Admin authentication required.
   *     tags: [Car Manufacturers]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Car manufacturer ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 100
   *                 description: Manufacturer name
   *                 example: "Toyota"
   *               country:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 50
   *                 description: Country of origin
   *                 example: "Japan"
   *               logo_url:
   *                 type: string
   *                 maxLength: 500
   *                 description: URL to manufacturer logo
   *                 example: "https://example.com/logos/toyota.png"
   *               website_url:
   *                 type: string
   *                 maxLength: 500
   *                 description: Official website URL
   *                 example: "https://toyota.com"
   *               description:
   *                 type: string
   *                 maxLength: 1000
   *                 description: Brief description of the manufacturer
   *                 example: "Japanese multinational automotive manufacturer"
   *             additionalProperties: false
   *     responses:
   *       200:
   *         description: Car manufacturer updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/CarManufacturer'
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       409:
   *         description: Manufacturer with this name already exists
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "Manufacturer name already exists"
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /car-manufacturers/{id}:
   *   delete:
   *     summary: Delete a car manufacturer
   *     description: Soft delete a car manufacturer. Admin authentication required. Note that this will affect all associated car models.
   *     tags: [Car Manufacturers]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Car manufacturer ID
   *     responses:
   *       200:
   *         description: Car manufacturer deleted successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "Car manufacturer deleted successfully"
   *                 deletedId:
   *                   type: string
   *                   format: uuid
   *                   example: "123e4567-e89b-12d3-a456-************"
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       409:
   *         description: Cannot delete manufacturer with associated models
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "Cannot delete manufacturer with associated car models"
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
} 