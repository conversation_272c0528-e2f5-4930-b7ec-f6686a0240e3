import { <PERSON><PERSON><PERSON>out<PERSON> } from '../crud';
import { Request, Response } from '../base';
import { usedCarInquiryController } from '@/controllers/crud/usedCarInquiryController';
import { authInfoMiddleware, queryMiddleware } from '@/middlewares';

export default class InquiriesRouter extends C<PERSON>Router<typeof usedCarInquiryController> {
  constructor() {
    super(usedCarInquiryController);
  }

  customRouting() {
    // GET /api/v1/inquiries - Get user's inquiries (sent or received)
    this.router.get(
      '/',
      [authInfoMiddleware.run(), queryMiddleware.run()],
      this.route(this.getUserInquiries)
    );

    // POST /api/v1/inquiries - Send an inquiry
    this.router.post(
      '/',
      [authInfoMiddleware.run()],
      this.route(this.createInquiry)
    );

    // GET /api/v1/inquiries/:id - Get inquiry details
    this.router.get(
      '/:id',
      [authInfoMiddleware.run()],
      this.route(this.getInquiryDetails)
    );

    // PUT /api/v1/inquiries/:id/status - Update inquiry status (seller only)
    this.router.put(
      '/:id/status',
      [authInfoMiddleware.run()],
      this.route(this.updateInquiryStatus)
    );

    // PUT /api/v1/inquiries/:id/read - Mark inquiry as read
    this.router.put(
      '/:id/read',
      [authInfoMiddleware.run()],
      this.route(this.markAsRead)
    );
  }

  async getUserInquiries(req: Request, res: Response) {
    const userId = req.tokenInfo.payload.user_id;
    const type = req.query.type as 'sent' | 'received' || 'sent';
    const result = await this.controller.getUserInquiries(userId, type, {
      queryInfo: req.queryInfo,
    });
    this.onSuccessAsList(res, result, undefined, req.queryInfo);
  }

  async createInquiry(req: Request, res: Response) {
    const userId = req.tokenInfo.payload.user_id;
    
    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
        used_car_post_id: {
          type: 'string',
          format: 'uuid'
        },
        inquiry_type: {
          type: 'string',
          enum: ['general', 'price_negotiation', 'test_drive', 'inspection', 'financing']
        },
        subject: {
          type: 'string',
          maxLength: 200
        },
        message: {
          type: 'string',
          minLength: 1,
          maxLength: 1000
        },
        buyer_name: {
          type: 'string',
          maxLength: 100
        },
        buyer_phone: {
          type: 'string',
          pattern: '^[0-9+\\-\\s()]+$'
        },
        buyer_email: {
          type: 'string',
          format: 'email'
        },
        preferred_contact_method: {
          type: 'string',
          enum: ['phone', 'email', 'in_app']
        },
        offered_price: {
          type: 'number',
          minimum: 0
        },
        is_serious_buyer: {
          type: 'boolean'
        },
        preferred_meeting_location: {
          type: 'string',
          maxLength: 200
        },
        available_dates: {
          type: 'string',
          maxLength: 500
        }
      },
      required: ['used_car_post_id', 'inquiry_type', 'message']
    });

    const params = {
      ...req.body,
      buyer_id: userId,
    };

    const result = await this.controller.createInquiry(params);
    this.onSuccess(res, result);
  }

  async getInquiryDetails(req: Request, res: Response) {
    const userId = req.tokenInfo.payload.user_id;
    const inquiryId = req.params.id;
    const result = await this.controller.getInquiryDetails(inquiryId, userId);
    this.onSuccess(res, result);
  }

  async updateInquiryStatus(req: Request, res: Response) {
    const userId = req.tokenInfo.payload.user_id;
    const inquiryId = req.params.id;
    
    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
        inquiry_status: {
          type: 'string',
          enum: ['pending', 'replied', 'in_negotiation', 'scheduled', 'closed', 'cancelled']
        },
        seller_response: {
          type: 'string',
          maxLength: 1000
        },
        meeting_scheduled_at: {
          type: 'string',
          format: 'date-time'
        }
      }
    });

    const result = await this.controller.updateInquiryStatus(inquiryId, userId, req.body);
    this.onSuccess(res, result);
  }

  async markAsRead(req: Request, res: Response) {
    const userId = req.tokenInfo.payload.user_id;
    const inquiryId = req.params.id;
    
    await this.validateJSON(req.body, {
      type: 'object',
      properties: {
        user_type: {
          type: 'string',
          enum: ['buyer', 'seller']
        }
      },
      required: ['user_type']
    });

    const { user_type } = req.body;
    const result = await this.controller.markAsRead(inquiryId, userId, user_type);
    this.onSuccess(res, result);
  }
} 