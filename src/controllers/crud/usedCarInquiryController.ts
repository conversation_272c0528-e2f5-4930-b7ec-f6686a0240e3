import { <PERSON><PERSON><PERSON><PERSON>roller } from '../crudController';
import { ICrudOption, usedCarInquiryService } from '@/services';

/**
 * @swagger
 * tags:
 *   name: Inquiries
 *   description: User inquiries for used car posts
 */

export class UsedCarInquiryController extends Crud<PERSON>ontroller<typeof usedCarInquiryService> {
  constructor() {
    super(usedCarInquiryService);
  }

  /**
   * @swagger
   * /inquiries:
   *   get:
   *     summary: Get all inquiries sent by the authenticated user
   *     tags: [Inquiries]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: type
   *         schema:
   *           type: string
   *           enum: [sent, received]
   *           default: sent
   *         description: Type of inquiries to retrieve
   *     responses:
   *       200:
   *         description: List of inquiries
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/UsedCarInquiry'
   *                 count:
   *                   type: integer
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async getUserInquiries(userId: string, type: 'sent' | 'received' = 'sent', option?: ICrudOption) {
    if (type === 'received') {
      return await this.service.getReceivedInquiries(userId, option);
    }
    return await this.service.getUserInquiries(userId, option);
  }

  /**
   * @swagger
   * /inquiries:
   *   post:
   *     summary: Send an inquiry to a seller
   *     tags: [Inquiries]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - used_car_post_id
   *               - inquiry_type
   *               - message
   *             properties:
   *               used_car_post_id:
   *                 type: string
   *                 format: uuid
   *                 description: ID of the used car post to inquire about
   *               inquiry_type:
   *                 type: string
   *                 enum: [general, price_negotiation, test_drive, inspection, financing]
   *                 description: Type of inquiry
   *               subject:
   *                 type: string
   *                 description: Subject/title of the inquiry
   *               message:
   *                 type: string
   *                 description: The inquiry message content
   *               buyer_name:
   *                 type: string
   *                 description: Name of the buyer
   *               buyer_phone:
   *                 type: string
   *                 description: Contact phone number
   *               buyer_email:
   *                 type: string
   *                 description: Contact email
   *               preferred_contact_method:
   *                 type: string
   *                 enum: [phone, email, in_app]
   *                 default: in_app
   *               offered_price:
   *                 type: number
   *                 description: Price offered by the buyer
   *               is_serious_buyer:
   *                 type: boolean
   *                 description: Whether the buyer is serious/ready to purchase
   *               preferred_meeting_location:
   *                 type: string
   *                 description: Preferred location for meeting
   *               available_dates:
   *                 type: string
   *                 description: Buyer's availability
   *     responses:
   *       201:
   *         description: Inquiry sent successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/UsedCarInquiry'
   *       400:
   *         description: Invalid input data
   *       404:
   *         description: Post not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async createInquiry(params: any, option?: ICrudOption) {
    return await this.service.createInquiry(params, option);
  }

  /**
   * @swagger
   * /inquiries/{id}:
   *   get:
   *     summary: Get inquiry details
   *     tags: [Inquiries]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Inquiry ID
   *     responses:
   *       200:
   *         description: Inquiry details
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/UsedCarInquiry'
   *       404:
   *         description: Inquiry not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async getInquiryDetails(inquiryId: string, userId: string, option?: ICrudOption) {
    return await this.service.getInquiryDetails(inquiryId, userId, option);
  }

  /**
   * @swagger
   * /inquiries/{id}/status:
   *   put:
   *     summary: Update inquiry status (seller only)
   *     tags: [Inquiries]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Inquiry ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               inquiry_status:
   *                 type: string
   *                 enum: [pending, replied, in_negotiation, scheduled, closed, cancelled]
   *               seller_response:
   *                 type: string
   *                 description: Seller's response message
   *               meeting_scheduled_at:
   *                 type: string
   *                 format: date-time
   *                 description: Scheduled meeting time
   *     responses:
   *       200:
   *         description: Inquiry status updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/UsedCarInquiry'
   *       404:
   *         description: Inquiry not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async updateInquiryStatus(inquiryId: string, userId: string, params: any, option?: ICrudOption) {
    return await this.service.updateInquiryStatus(inquiryId, userId, params, option);
  }

  /**
   * @swagger
   * /inquiries/{id}/read:
   *   put:
   *     summary: Mark inquiry as read
   *     tags: [Inquiries]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Inquiry ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - user_type
   *             properties:
   *               user_type:
   *                 type: string
   *                 enum: [buyer, seller]
   *                 description: Type of user marking as read
   *     responses:
   *       200:
   *         description: Inquiry marked as read successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/UsedCarInquiry'
   *       404:
   *         description: Inquiry not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async markAsRead(inquiryId: string, userId: string, userType: 'buyer' | 'seller', option?: ICrudOption) {
    return await this.service.markAsRead(inquiryId, userId, userType, option);
  }
}

export const usedCarInquiryController = new UsedCarInquiryController(); 