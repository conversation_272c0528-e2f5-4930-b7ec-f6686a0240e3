const jwt = require('jwt-simple');
const moment = require('moment');

// Server secret from .env file
const secret = '9nXvVQQCCt9U07EC7OFKTayHBEalDn_LOCAL_DEV';

const payload = {
  employee_id: 'test-admin-id',
  role: 'ADMIN'
};

const token = jwt.encode({
  payload: payload,
  role: 'ADMIN',
  exp: moment().add(1, 'day')
}, secret);

console.log('Admin Token:', token);
console.log('Use this token in Authorization header as: Bearer', token);
