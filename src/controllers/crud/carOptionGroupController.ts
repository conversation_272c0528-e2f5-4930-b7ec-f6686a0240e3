import { <PERSON><PERSON><PERSON>ontroller } from '../crudController';
import { carOptionGroupService } from '@/services';

/**
 * @swagger
 * tags:
 *   name: Car Option Groups
 *   description: Car option group management and operations (Admin only)
 */
export class CarOptionGroupController extends C<PERSON><PERSON>ontroller<typeof carOptionGroupService> {
  constructor() {
    super(carOptionGroupService);
  }

  /**
   * @swagger
   * /car-option-groups:
   *   get:
   *     summary: Get list of car option groups
   *     description: Retrieve a paginated list of car option groups. Admin authentication required.
   *     tags: [Car Option Groups]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PaginationLimit'
   *       - $ref: '#/components/parameters/PaginationOffset'
   *       - $ref: '#/components/parameters/FilterParam'
   *       - $ref: '#/components/parameters/SortParam'
   *       - $ref: '#/components/parameters/SearchParam'
   *       - in: query
   *         name: status
   *         schema:
   *           type: boolean
   *         description: Filter by active status
   *     responses:
   *       200:
   *         description: List of car option groups retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/CarOptionGroup'
   *                 count:
   *                   type: integer
   *                   description: Total number of option groups
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /car-option-groups/{id}:
   *   get:
   *     summary: Get a specific car option group
   *     description: Retrieve details of a specific car option group by ID. Admin authentication required.
   *     tags: [Car Option Groups]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Car option group ID
   *     responses:
   *       200:
   *         description: Car option group details retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/CarOptionGroup'
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /car-option-groups:
   *   post:
   *     summary: Create a new car option group
   *     description: Create a new car option group entry. Admin authentication required.
   *     tags: [Car Option Groups]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *             properties:
   *               name:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 100
   *                 description: Option group name (e.g., Safety, Comfort, Technology)
   *                 example: "Safety"
   *               description:
   *                 type: string
   *                 maxLength: 1000
   *                 description: Description of the option group
   *                 example: "Safety-related features and options"
   *               icon:
   *                 type: string
   *                 maxLength: 100
   *                 description: Icon identifier or URL for the group
   *                 example: "safety-shield"
   *               color:
   *                 type: string
   *                 maxLength: 50
   *                 description: Color code for UI representation
   *                 example: "#FF5722"
   *               index:
   *                 type: integer
   *                 minimum: 0
   *                 description: Display order
   *                 example: 0
   *             additionalProperties: false
   *     responses:
   *       201:
   *         description: Car option group created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/CarOptionGroup'
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       409:
   *         description: Option group with this name already exists
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "Option group name already exists"
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /car-option-groups/{id}:
   *   put:
   *     summary: Update a car option group
   *     description: Update an existing car option group. Admin authentication required.
   *     tags: [Car Option Groups]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Car option group ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 100
   *                 description: Option group name
   *                 example: "Safety"
   *               description:
   *                 type: string
   *                 maxLength: 1000
   *                 description: Description of the option group
   *                 example: "Safety-related features and options"
   *               icon:
   *                 type: string
   *                 maxLength: 100
   *                 description: Icon identifier or URL for the group
   *                 example: "safety-shield"
   *               color:
   *                 type: string
   *                 maxLength: 50
   *                 description: Color code for UI representation
   *                 example: "#FF5722"
   *               index:
   *                 type: integer
   *                 minimum: 0
   *                 description: Display order
   *                 example: 0
   *             additionalProperties: false
   *     responses:
   *       200:
   *         description: Car option group updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/CarOptionGroup'
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       409:
   *         description: Option group with this name already exists
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "Option group name already exists"
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /car-option-groups/{id}:
   *   delete:
   *     summary: Delete a car option group
   *     description: Soft delete a car option group. Admin authentication required. Note that this will affect all associated car options.
   *     tags: [Car Option Groups]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Car option group ID
   *     responses:
   *       200:
   *         description: Car option group deleted successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "Car option group deleted successfully"
   *                 deletedId:
   *                   type: string
   *                   format: uuid
   *                   example: "123e4567-e89b-12d3-a456-************"
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       409:
   *         description: Cannot delete option group with associated options
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "Cannot delete option group with associated car options"
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
}
