import { <PERSON><PERSON><PERSON><PERSON>roller } from '../crudController';
import { ICrudOption, usedCarSavedSearchService } from '@/services';

/**
 * @swagger
 * tags:
 *   name: Saved Searches
 *   description: User saved search management for used car posts
 */

export class UsedCarSavedSearchController extends Crud<PERSON><PERSON>roller<typeof usedCarSavedSearchService> {
  constructor() {
    super(usedCarSavedSearchService);
  }

  /**
   * @swagger
   * /saved-searches:
   *   get:
   *     summary: Get all saved searches for the authenticated user
   *     tags: [Saved Searches]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: List of saved searches
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/UsedCarSavedSearch'
   *                 count:
   *                   type: integer
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async getUserSavedSearches(userId: string, option?: ICrudOption) {
    return await this.service.getUserSavedSearches(userId, option);
  }

  /**
   * @swagger
   * /saved-searches:
   *   post:
   *     summary: Create a new saved search
   *     tags: [Saved Searches]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *             properties:
   *               name:
   *                 type: string
   *                 description: Name for the saved search
   *                 example: "Budget Family Car"
   *               manufacturer_id:
   *                 type: string
   *                 format: uuid
   *                 description: Filter by manufacturer
   *               model_id:
   *                 type: string
   *                 format: uuid
   *                 description: Filter by car model
   *               category_id:
   *                 type: string
   *                 format: uuid
   *                 description: Filter by category
   *               price_min:
   *                 type: number
   *                 description: Minimum price
   *               price_max:
   *                 type: number
   *                 description: Maximum price
   *               year_min:
   *                 type: integer
   *                 description: Minimum year
   *               year_max:
   *                 type: integer
   *                 description: Maximum year
   *               mileage_min:
   *                 type: integer
   *                 description: Minimum mileage
   *               mileage_max:
   *                 type: integer
   *                 description: Maximum mileage
   *               fuel_type:
   *                 type: string
   *                 enum: [gasoline, diesel, hybrid, electric, lpg]
   *               transmission:
   *                 type: string
   *                 enum: [manual, automatic, cvt]
   *               body_type:
   *                 type: string
   *                 enum: [sedan, hatchback, suv, coupe, convertible, wagon, pickup, van, minivan]
   *               condition:
   *                 type: string
   *                 enum: [excellent, good, fair, poor]
   *               location_radius:
   *                 type: number
   *                 description: Search radius in kilometers
   *               location_latitude:
   *                 type: number
   *               location_longitude:
   *                 type: number
   *               keywords:
   *                 type: string
   *                 description: Search keywords
   *               is_negotiable:
   *                 type: boolean
   *               notification_enabled:
   *                 type: boolean
   *                 description: Enable notifications for new matches
   *     responses:
   *       201:
   *         description: Saved search created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/UsedCarSavedSearch'
   *       400:
   *         description: Invalid input data
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async createSavedSearch(params: any, option?: ICrudOption) {
    return await this.service.createSavedSearch(params, option);
  }

  /**
   * @swagger
   * /saved-searches/{id}:
   *   delete:
   *     summary: Delete a saved search
   *     tags: [Saved Searches]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Saved search ID
   *     responses:
   *       200:
   *         description: Saved search deleted successfully
   *       404:
   *         description: Saved search not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async deleteSavedSearch(searchId: string, userId: string, option?: ICrudOption) {
    return await this.service.deleteSavedSearch(searchId, userId, option);
  }

  /**
   * @swagger
   * /saved-searches/{id}:
   *   put:
   *     summary: Update a saved search
   *     tags: [Saved Searches]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Saved search ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UsedCarSavedSearch'
   *     responses:
   *       200:
   *         description: Saved search updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/UsedCarSavedSearch'
   *       404:
   *         description: Saved search not found
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   */
  async updateSavedSearch(searchId: string, userId: string, params: any, option?: ICrudOption) {
    return await this.service.updateSavedSearch(searchId, userId, params, option);
  }
}

export const usedCarSavedSearchController = new UsedCarSavedSearchController(); 