import { <PERSON>rudController } from '../crudController';
import { carModelService } from '@/services';

/**
 * @swagger
 * tags:
 *   name: Car Models
 *   description: Car model management and operations (Admin only)
 */
export class CarModelController extends CrudController<typeof carModelService> {
  constructor() {
    super(carModelService);
  }

  /**
   * @swagger
   * /car-models:
   *   get:
   *     summary: Get list of car models
   *     description: Retrieve a paginated list of car models. Admin authentication required.
   *     tags: [Car Models]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PaginationLimit'
   *       - $ref: '#/components/parameters/PaginationOffset'
   *       - $ref: '#/components/parameters/FilterParam'
   *       - $ref: '#/components/parameters/SortParam'
   *       - $ref: '#/components/parameters/SearchParam'
   *       - in: query
   *         name: status
   *         schema:
   *           type: boolean
   *         description: Filter by active status
   *       - in: query
   *         name: manufacturer_id
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filter by manufacturer ID
   *       - in: query
   *         name: body_type
   *         schema:
   *           type: string
   *           enum: [sedan, suv, hatchback, coupe, convertible, wagon, pickup, van]
   *         description: Filter by body type
   *       - in: query
   *         name: fuel_type
   *         schema:
   *           type: string
   *           enum: [gasoline, diesel, hybrid, electric, lpg]
   *         description: Filter by fuel type
   *     responses:
   *       200:
   *         description: List of car models retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/CarModel'
   *                 count:
   *                   type: integer
   *                   description: Total number of models
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /car-models/{id}:
   *   get:
   *     summary: Get a specific car model
   *     description: Retrieve details of a specific car model by ID. Admin authentication required.
   *     tags: [Car Models]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Car model ID
   *     responses:
   *       200:
   *         description: Car model details retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/CarModel'
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /car-models:
   *   post:
   *     summary: Create a new car model
   *     description: Create a new car model entry. Admin authentication required.
   *     tags: [Car Models]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *               - manufacturer_id
   *             properties:
   *               name:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 100
   *                 description: Model name (e.g., Camry, Civic, 3 Series)
   *                 example: "Camry"
   *               manufacturer_id:
   *                 type: string
   *                 format: uuid
   *                 description: ID of the car manufacturer
   *                 example: "123e4567-e89b-12d3-a456-************"
   *               body_type:
   *                 type: string
   *                 enum: [sedan, suv, hatchback, coupe, convertible, wagon, pickup, van]
   *                 description: Body type of the model
   *                 example: "sedan"
   *               fuel_type:
   *                 type: string
   *                 enum: [gasoline, diesel, hybrid, electric, lpg]
   *                 description: Fuel type of the model
   *                 example: "gasoline"
   *               transmission:
   *                 type: string
   *                 enum: [manual, automatic, cvt]
   *                 description: Transmission type
   *                 example: "automatic"
   *               engine_size:
   *                 type: string
   *                 maxLength: 50
   *                 description: Engine size/displacement (e.g., 2.0L, 1.5T)
   *                 example: "2.5L"
   *               start_year:
   *                 type: integer
   *                 minimum: 1900
   *                 maximum: 2030
   *                 description: First production year
   *                 example: 2020
   *               end_year:
   *                 type: integer
   *                 minimum: 1900
   *                 maximum: 2030
   *                 description: Last production year (null if still in production)
   *                 example: 2024
   *               description:
   *                 type: string
   *                 maxLength: 1000
   *                 description: Brief description of the model
   *                 example: "Mid-size sedan known for reliability and fuel efficiency"
   *               image_url:
   *                 type: string
   *                 maxLength: 500
   *                 description: URL to model image
   *                 example: "https://example.com/images/camry.jpg"
   *             additionalProperties: false
   *     responses:
   *       201:
   *         description: Car model created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/CarModel'
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       409:
   *         description: Model with this name already exists for this manufacturer
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "Model name already exists for this manufacturer"
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /car-models/{id}:
   *   put:
   *     summary: Update a car model
   *     description: Update an existing car model. Admin authentication required.
   *     tags: [Car Models]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Car model ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 100
   *                 description: Model name
   *                 example: "Camry"
   *               manufacturer_id:
   *                 type: string
   *                 format: uuid
   *                 description: ID of the car manufacturer
   *                 example: "123e4567-e89b-12d3-a456-************"
   *               body_type:
   *                 type: string
   *                 enum: [sedan, suv, hatchback, coupe, convertible, wagon, pickup, van]
   *                 description: Body type of the model
   *                 example: "sedan"
   *               fuel_type:
   *                 type: string
   *                 enum: [gasoline, diesel, hybrid, electric, lpg]
   *                 description: Fuel type of the model
   *                 example: "gasoline"
   *               transmission:
   *                 type: string
   *                 enum: [manual, automatic, cvt]
   *                 description: Transmission type
   *                 example: "automatic"
   *               engine_size:
   *                 type: string
   *                 maxLength: 50
   *                 description: Engine size/displacement
   *                 example: "2.5L"
   *               start_year:
   *                 type: integer
   *                 minimum: 1900
   *                 maximum: 2030
   *                 description: First production year
   *                 example: 2020
   *               end_year:
   *                 type: integer
   *                 minimum: 1900
   *                 maximum: 2030
   *                 description: Last production year
   *                 example: 2024
   *               description:
   *                 type: string
   *                 maxLength: 1000
   *                 description: Brief description of the model
   *                 example: "Mid-size sedan known for reliability and fuel efficiency"
   *               image_url:
   *                 type: string
   *                 maxLength: 500
   *                 description: URL to model image
   *                 example: "https://example.com/images/camry.jpg"
   *             additionalProperties: false
   *     responses:
   *       200:
   *         description: Car model updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/CarModel'
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       409:
   *         description: Model with this name already exists for this manufacturer
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "Model name already exists for this manufacturer"
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /car-models/{id}:
   *   delete:
   *     summary: Delete a car model
   *     description: Soft delete a car model. Admin authentication required. Note that this will affect all associated used car posts.
   *     tags: [Car Models]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Car model ID
   *     responses:
   *       200:
   *         description: Car model deleted successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "Car model deleted successfully"
   *                 deletedId:
   *                   type: string
   *                   format: uuid
   *                   example: "123e4567-e89b-12d3-a456-************"
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       409:
   *         description: Cannot delete model with associated used car posts
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "Cannot delete model with associated used car posts"
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
}
