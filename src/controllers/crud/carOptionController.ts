import { <PERSON>rud<PERSON>ontroller } from '../crudController';
import { carOptionService } from '@/services';

/**
 * @swagger
 * tags:
 *   name: Car Options
 *   description: Car option management and operations (Admin only)
 */
export class CarOptionController extends CrudController<typeof carOptionService> {
  constructor() {
    super(carOptionService);
  }

  /**
   * @swagger
   * /car-options:
   *   get:
   *     summary: Get list of car options
   *     description: Retrieve a paginated list of car options. Admin authentication required.
   *     tags: [Car Options]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - $ref: '#/components/parameters/PaginationLimit'
   *       - $ref: '#/components/parameters/PaginationOffset'
   *       - $ref: '#/components/parameters/FilterParam'
   *       - $ref: '#/components/parameters/SortParam'
   *       - $ref: '#/components/parameters/SearchParam'
   *       - in: query
   *         name: status
   *         schema:
   *           type: boolean
   *         description: Filter by active status
   *       - in: query
   *         name: option_group_id
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filter by option group ID
   *       - in: query
   *         name: is_premium
   *         schema:
   *           type: boolean
   *         description: Filter by premium status
   *     responses:
   *       200:
   *         description: List of car options retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 rows:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/CarOption'
   *                 count:
   *                   type: integer
   *                   description: Total number of options
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /car-options/{id}:
   *   get:
   *     summary: Get a specific car option
   *     description: Retrieve details of a specific car option by ID. Admin authentication required.
   *     tags: [Car Options]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Car option ID
   *     responses:
   *       200:
   *         description: Car option details retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/CarOption'
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /car-options:
   *   post:
   *     summary: Create a new car option
   *     description: Create a new car option entry. Admin authentication required.
   *     tags: [Car Options]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *               - option_group_id
   *             properties:
   *               name:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 100
   *                 description: Option name (e.g., ABS, Air Conditioning, Navigation)
   *                 example: "ABS"
   *               description:
   *                 type: string
   *                 maxLength: 1000
   *                 description: Detailed description of the option
   *                 example: "Anti-lock Braking System for enhanced safety"
   *               option_group_id:
   *                 type: string
   *                 format: uuid
   *                 description: ID of the option group this belongs to
   *                 example: "123e4567-e89b-12d3-a456-************"
   *               icon:
   *                 type: string
   *                 maxLength: 100
   *                 description: Icon identifier or URL for the option
   *                 example: "abs-icon"
   *               is_premium:
   *                 type: boolean
   *                 description: Whether this is considered a premium option
   *                 example: false
   *               index:
   *                 type: integer
   *                 minimum: 0
   *                 description: Display order within the group
   *                 example: 0
   *             additionalProperties: false
   *     responses:
   *       201:
   *         description: Car option created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/CarOption'
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       409:
   *         description: Option with this name already exists in this group
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "Option name already exists in this group"
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /car-options/{id}:
   *   put:
   *     summary: Update a car option
   *     description: Update an existing car option. Admin authentication required.
   *     tags: [Car Options]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Car option ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 minLength: 1
   *                 maxLength: 100
   *                 description: Option name
   *                 example: "ABS"
   *               description:
   *                 type: string
   *                 maxLength: 1000
   *                 description: Detailed description of the option
   *                 example: "Anti-lock Braking System for enhanced safety"
   *               option_group_id:
   *                 type: string
   *                 format: uuid
   *                 description: ID of the option group this belongs to
   *                 example: "123e4567-e89b-12d3-a456-************"
   *               icon:
   *                 type: string
   *                 maxLength: 100
   *                 description: Icon identifier or URL for the option
   *                 example: "abs-icon"
   *               is_premium:
   *                 type: boolean
   *                 description: Whether this is considered a premium option
   *                 example: false
   *               index:
   *                 type: integer
   *                 minimum: 0
   *                 description: Display order within the group
   *                 example: 0
   *             additionalProperties: false
   *     responses:
   *       200:
   *         description: Car option updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/CarOption'
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       409:
   *         description: Option with this name already exists in this group
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "Option name already exists in this group"
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */

  /**
   * @swagger
   * /car-options/{id}:
   *   delete:
   *     summary: Delete a car option
   *     description: Soft delete a car option. Admin authentication required. Note that this will affect all associated used car posts.
   *     tags: [Car Options]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Car option ID
   *     responses:
   *       200:
   *         description: Car option deleted successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "Car option deleted successfully"
   *                 deletedId:
   *                   type: string
   *                   format: uuid
   *                   example: "123e4567-e89b-12d3-a456-************"
   *       400:
   *         $ref: '#/components/responses/BadRequestError'
   *       401:
   *         $ref: '#/components/responses/UnauthorizedError'
   *       403:
   *         $ref: '#/components/responses/ForbiddenError'
   *       404:
   *         $ref: '#/components/responses/NotFoundError'
   *       409:
   *         description: Cannot delete option with associated used car posts
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 message:
   *                   type: string
   *                   example: "Cannot delete option with associated used car posts"
   *       500:
   *         $ref: '#/components/responses/ServerError'
   */
}
