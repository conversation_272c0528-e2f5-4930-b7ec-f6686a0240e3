import { CrudService, ICrudOption } from '../crudService.pg';
import { CarOptionGroup, CarOption } from '@/models';
import { config } from '@/config';
import { errorService } from '@/services';

export class CarOptionGroupService extends CrudService<typeof CarOptionGroup> {
  constructor() {
    super(CarOptionGroup);
  }

  async getList(
    option: ICrudOption = {
      limit: config.database.defaultPageSize,
      offset: 0,
      scope: ['defaultScope'],
    }
  ) {
    return await this.exec(
      this.modelWithScope(option.scope).findAll({
        include: [
          {
            association: 'options',
            attributes: ['id', 'name', 'description']
          }
        ],
        limit: option.limit,
        offset: option.offset,
        order: [['index', 'ASC'], ['created_at', 'DESC']],
        where: option.filter || {}
      })
    );
  }

  async getItem(option: ICrudOption) {
    return await this.exec(
      this.modelWithScope(option.scope).findOne({
        include: [
          {
            association: 'options',
            attributes: ['id', 'name', 'description']
          }
        ],
        where: option.filter
      })
    );
  }

  /**
   * Override delete method to check for relationship constraints
   * Prevents deletion of option group if it has associated car options
   */
  async delete(option?: ICrudOption) {
    // First check if the option group exists
    const optionGroup = await this.getItem(option);
    
    // Check for associated car options
    const associatedOptions = await this.exec(
      CarOption.count({
        where: { 
          option_group_id: optionGroup.id,
          deleted_at: null  // Only count non-deleted options
        }
      })
    );

    if (associatedOptions > 0) {
      throw errorService.database.queryFail(
        `Cannot delete option group with ${associatedOptions} associated car option(s). Please delete or reassign the options first.`,
        409
      );
    }

    // Proceed with deletion if no constraints violated
    return await super.delete(option);
  }

  /**
   * Enhanced create method with validation
   */
  async create(params: any, option?: ICrudOption) {
    // Validate required fields
    if (!params.name || !params.name.trim()) {
      throw errorService.database.queryFail('Option group name is required', 400);
    }

    // Check for duplicate name (case-insensitive)
    const existingOptionGroup = await this.exec(
      CarOptionGroup.findOne({
        where: {
          name: {
            [require('sequelize').Op.iLike]: params.name.trim()
          }
        }
      }),
      { allowNull: true }
    );

    if (existingOptionGroup) {
      throw errorService.database.queryFail('Option group with this name already exists', 409);
    }

    return await super.create(params, option);
  }

  /**
   * Enhanced update method with validation
   */
  async update(params: any, option?: ICrudOption) {
    // Validate name if provided
    if (params.name && !params.name.trim()) {
      throw errorService.database.queryFail('Option group name cannot be empty', 400);
    }

    // Check for duplicate name if name is being updated
    if (params.name) {
      const existingOptionGroup = await this.exec(
        CarOptionGroup.findOne({
          where: {
            name: {
              [require('sequelize').Op.iLike]: params.name.trim()
            },
            id: {
              [require('sequelize').Op.ne]: option.filter.id
            }
          }
        }),
        { allowNull: true }
      );

      if (existingOptionGroup) {
        throw errorService.database.queryFail('Option group with this name already exists', 409);
      }
    }

    return await super.update(params, option);
  }

  /**
   * Get option group with associated options count
   */
  async getOptionGroupWithOptionsCount(optionGroupId: string) {
    const optionGroup = await this.exec(
      CarOptionGroup.findByPk(optionGroupId, {
        include: [{
          model: CarOption,
          as: 'options',
          attributes: ['id'],
          where: { deleted_at: null },
          required: false
        }]
      }),
      { allowNull: false }
    );

    return {
      ...optionGroup.toJSON(),
      options_count: optionGroup.options ? optionGroup.options.length : 0
    };
  }
}
